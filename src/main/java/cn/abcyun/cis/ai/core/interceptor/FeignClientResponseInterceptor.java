package cn.abcyun.cis.ai.core.interceptor;

import cn.abcyun.cis.ai.core.supplier.SupplierServiceFactory;
import cn.abcyun.cis.commons.util.PropertyUtils;
import feign.Request;
import feign.RequestTemplate;
import feign.Response;
import feign.Target;
import feign.codec.Decoder;
import org.reactivestreams.Publisher;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.cloud.openfeign.support.ResponseEntityDecoder;
import org.springframework.cloud.openfeign.support.SpringDecoder;
import org.springframework.core.io.buffer.DataBufferUtils;
import reactivefeign.client.ReactiveHttpRequest;
import reactivefeign.client.ReactiveHttpResponse;
import reactivefeign.client.ReactiveHttpResponseMapper;
import reactivefeign.utils.HttpStatus;
import reactor.core.publisher.Mono;

import java.awt.image.DataBuffer;
import java.awt.image.DataBufferUShort;
import java.io.IOException;
import java.lang.reflect.Type;
import java.util.*;

import static reactivefeign.utils.FeignUtils.httpMethod;

/**
 * <AUTHOR>
 * @date 2025-05-20 13:42
 */
public class FeignClientResponseInterceptor extends ResponseEntityDecoder implements ReactiveHttpResponseMapper<Publisher<Object>> {

    public FeignClientResponseInterceptor(ObjectFactory<HttpMessageConverters> messageConverters) {
        super(new SpringDecoder(messageConverters));
    }

    @Override
    public Mono<ReactiveHttpResponse<Publisher<Object>>> apply(ReactiveHttpResponse<Publisher<Object>> response) {
        Target<?> target = (Target<?>) PropertyUtils.getProperty(response.request(), "target");
        return response.bodyData()
                .defaultIfEmpty(new byte[0])
                .handle((bodyData, sink) -> {
                    Response feignResponse = buildFeignResponseForDecoder(response, bodyData);
                    Object data;
                    try {
                        data = SupplierServiceFactory.getInstance().getSupplierService(target).decode(feignResponse, target.type(), super.decode(feignResponse, target.type()));
                    } catch (Exception e) {
                        sink.error(e);
                        return;
                    }
                    sink.next(new ReactiveHttpResponse<Publisher<Object>>() {

                        @Override
                        public ReactiveHttpRequest request() {
                            return response.request();
                        }

                        @Override
                        public int status() {
                            return response.status();
                        }

                        @Override
                        public Map<String, List<String>> headers() {
                            return response.headers();
                        }

                        @Override
                        public Publisher<Object> body() {
                            return Mono.just(data);
                        }

                        @Override
                        public Mono<Void> releaseBody() {
                            return
                        }

                        @Override
                        public Mono<byte[]> bodyData() {
                            return
                        }
                    });
                });
    }

    private static Response buildFeignResponseForDecoder(ReactiveHttpResponse<Publisher<Object>> response, byte[] bodyData) {
        ReactiveHttpRequest request = response.request();
        //noinspection unchecked,rawtypes
        Request feignRequest = Request.create(httpMethod(request.method()),
                request.uri().toString(),
                (Map) request.headers(),
                Request.Body.empty(),
                null);

        return Response.builder()
                .request(feignRequest)
                .status(response.status())
                .reason(HttpStatus.getStatusText(response.status()))
                .headers(new HashMap<>(response.headers()))
                .body(bodyData).build();
    }
}
