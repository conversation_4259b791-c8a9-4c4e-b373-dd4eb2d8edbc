package cn.abcyun.cis.ai.core.conf;

import cn.abcyun.cis.ai.core.interceptor.FeignClientRequestInterceptor;
import cn.abcyun.cis.ai.core.interceptor.FeignClientResponseInterceptor;
import feign.Retryer;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.context.annotation.Bean;

/**
 * <AUTHOR>
 * @date 2025-04-23 11:13
 */
public class FeignClientConfiguration {

    @Bean
    public Retryer feignRetryer() {
        return new Retryer.Default();
    }

    @Bean
    public FeignClientRequestInterceptor feignClientRequestInterceptor() {
        return new FeignClientRequestInterceptor();
    }

    @Bean
    public FeignClientResponseInterceptor feignClientResponseInterceptor(ObjectFactory<HttpMessageConverters> messageConverters) {
        return new FeignClientResponseInterceptor(messageConverters);
    }
}
