package cn.abcyun.cis.ai.core.conf;

import cn.abcyun.cis.ai.core.interceptor.FeignClientResponseInterceptor;
import cn.abcyun.cis.commons.util.PropertyUtils;
import reactivefeign.ReactiveFeignBuilder;
import reactivefeign.spring.config.ReactiveFeignBasicConfigurator;
import reactivefeign.spring.config.ReactiveFeignNamedContext;

public class ReactiveFeignBuilderConfigurator extends ReactiveFeignBasicConfigurator {

    public ReactiveFeignBuilderConfigurator() {
        PropertyUtils.setProperty(this, "order", 0);
    }

    @Override
    public ReactiveFeignBuilder<?> configure(ReactiveFeignBuilder builder, ReactiveFeignNamedContext namedContext) {
        builder = super.configure(builder, namedContext);
        FeignClientResponseInterceptor responseInterceptor = namedContext.getOptional(FeignClientResponseInterceptor.class);
        if (responseInterceptor != null) {
            //noinspection unchecked
            builder.responseMapper(responseInterceptor);
        }
        return builder;
    }
}
