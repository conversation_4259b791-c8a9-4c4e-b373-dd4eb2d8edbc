package cn.abcyun.cis.ai.core.conf;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Scope;
import org.springframework.web.reactive.function.client.WebClient;
import reactivefeign.ReactiveFeignBuilder;
import reactivefeign.webclient.WebClientFeignCustomizer;

/**
 * <AUTHOR>
 * @date 2025-05-30 10:32
 */
@Configuration
public class ReactiveFeignConfiguration {

    @Bean
    @Primary
    @Scope("prototype")
    public ReactiveFeignBuilderConfigurator reactiveFeignBuilderConfigurator() {
        return new ReactiveFeignBuilderConfigurator();
    }

    @Bean
    @Primary
    @Scope("prototype")
    public ReactiveFeignBuilder<?> reactiveFeignBuilder(WebClient.Builder builder, @Autowired(required = false) WebClientFeignCustomizer webClientCustomizer) {
        return webClientCustomizer != null ? WebReactiveFeignImpl.builder(builder, webClientCustomizer) : WebReactiveFeignImpl.builder(builder);
    }
}
