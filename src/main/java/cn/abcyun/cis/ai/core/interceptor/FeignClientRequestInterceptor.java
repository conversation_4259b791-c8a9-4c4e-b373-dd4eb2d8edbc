package cn.abcyun.cis.ai.core.interceptor;

import cn.abcyun.cis.ai.core.supplier.SupplierServiceFactory;
import cn.abcyun.cis.commons.util.PropertyUtils;
import cn.abcyun.cis.core.util.SpringUtils;
import feign.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import reactivefeign.client.*;
import reactor.core.publisher.Mono;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-02-26 20:51
 */
@Slf4j
public class FeignClientRequestInterceptor implements ReactiveHttpRequestInterceptor {

    private final Map<Class<?>, String> targetTypeNameMap = new ConcurrentHashMap<>();

    @Override
    public Mono<ReactiveHttpRequest> apply(ReactiveHttpRequest reactiveHttpRequest) {
        if (MapUtils.isEmpty(targetTypeNameMap)) {
            try {
                Map<String, ?> reactiveFeignClientFactoryBeanMap = SpringUtils.getBeansOfType(Class.forName("reactivefeign.spring.config.ReactiveFeignClientFactoryBean"));
                targetTypeNameMap.putAll(
                        reactiveFeignClientFactoryBeanMap.values()
                                .stream()
                                .collect(Collectors.toMap(e -> (Class<?>) PropertyUtils.getProperty(e, "type"), e -> (String) PropertyUtils.getProperty(e, "name"), (a, b) -> a))
                );
            } catch (ClassNotFoundException e) {
                throw new RuntimeException(e);
            }
        }
        Target<?> target = (Target<?>) PropertyUtils.getProperty(reactiveHttpRequest, "target");
        PropertyUtils.setProperty(target, "name", targetTypeNameMap.get(target.type()));
        return SupplierServiceFactory.getInstance().getSupplierService(target).apply(reactiveHttpRequest);
    }
}
