package cn.abcyun.cis.ai.core.conf;

import lombok.Getter;
import org.springframework.web.reactive.function.client.WebClient;
import reactivefeign.webclient.WebClientFeignCustomizer;
import reactivefeign.webclient.WebReactiveFeign;

/**
 * <AUTHOR>
 * @date 2025-05-30 10:27
 */
public class WebReactiveFeignImpl extends WebReactiveFeign {

    public static <T> WebReactiveFeign.Builder<T> builder() {
        return builder(WebClient.builder());
    }

    public static <T> WebReactiveFeign.Builder<T> builder(WebClient.Builder webClientBuilder) {
        return new Builder<>(webClientBuilder);
    }

    public static <T> WebReactiveFeign.Builder<T> builder(WebClient.Builder webClientBuilder, WebClientFeignCustomizer webClientCustomizer) {
        return new Builder<>(webClientBuilder, webClientCustomizer);
    }

    @Getter
    public static class Builder<T> extends WebReactiveFeign.Builder<T> {

        private String clientName;

        protected Builder(WebClient.Builder webClientBuilder) {
            super(webClientBuilder);
        }

        protected Builder(WebClient.Builder webClientBuilder, WebClientFeignCustomizer webClientCustomizer) {
            super(webClientBuilder, webClientCustomizer);
        }

        public Builder<T> clientName(String clientName) {
            this.clientName = clientName;
            return this;
        }

        @Override
        public T target(Class<T> apiType, String url) {
            return super.target(apiType, clientName, url);
        }
    }
}
