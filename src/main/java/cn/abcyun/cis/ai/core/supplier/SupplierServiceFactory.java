package cn.abcyun.cis.ai.core.supplier;

import feign.Target;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.aop.support.AopUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 供应商服务工厂
 *
 * <AUTHOR>
 * @date 2025-03-19 16:36:19
 */
@Component
public class SupplierServiceFactory {

    private final Map<Pair<Class<? extends SupplierService>, String>, SupplierService> supplierServiceMap = new HashMap<>();

    private static ApplicationContext APPLICATION_CONTEXT;

    public SupplierServiceFactory(ApplicationContext applicationContext) {
        APPLICATION_CONTEXT = applicationContext;
    }

    public static SupplierServiceFactory getInstance() {
        return APPLICATION_CONTEXT.getBean(SupplierServiceFactory.class);
    }

    // 修改为延迟加载模式
    private Map<Pair<Class<? extends SupplierService>, String>, SupplierService> getSupplierServiceMap() {
        if (MapUtils.isEmpty(supplierServiceMap)) {
            synchronized (this) {
                if (MapUtils.isEmpty(supplierServiceMap)) {
                    // 延迟初始化，确保所有服务都已经被Spring容器创建
                    supplierServiceMap.putAll(
                            APPLICATION_CONTEXT.getBeansOfType(SupplierService.class)
                                    .values().stream()
                                    .collect(Collectors.toMap(
                                            supplierService -> Pair.of(
                                                    AopUtils.getTargetClass(supplierService).getAnnotation(SupplierIdentifier.class).value(),
                                                    supplierService.getSupplierId()
                                            ),
                                            Function.identity(),
                                            (a, b) -> a
                                    ))
                    );
                }
            }
        }
        return supplierServiceMap;
    }

    public <T extends SupplierService> T getSupplierService(String supplierId, Class<T> identifier) {
        Map<Pair<Class<? extends SupplierService>, String>, SupplierService> supplierServiceMap = getSupplierServiceMap();
        Pair<Class<T>, String> key = Pair.of(identifier, supplierId);
        if (!supplierServiceMap.containsKey(key)) {
            throw new IllegalArgumentException("Unexpected supplierService constant: " + supplierId);
        }
        return identifier.cast(supplierServiceMap.get(key));
    }

    public <T extends SupplierService> T getSupplierService(Target<?> target) {
        String name = target.name();
        String supplierId = StringUtils.substring(name, name.lastIndexOf("#") + 1);
        //noinspection unchecked
        Class<T> identifier = (Class<T>) target.type().getAnnotation(SupplierIdentifier.class).value();
        //template.target(supplier.getApiUrl());
        return getSupplierService(supplierId, identifier);
    }
}
